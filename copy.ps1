# === CONFIGURATION ===
$basePath = "Data/Prod_data_dataset/SS-00511"
$baseUrl = "https://evidentai.blob.core.windows.net/tpi/$basePath"
$sasToken = "?sv=2023-01-03&se=2025-08-24T12%3A50%3A57Z&sr=c&sp=rl&sig=fIDJkquvkUHsL3t6zgIIJe9mj7O2xoUY7tXBz2BR1aQ%3D"
$destination = "C:\Users\<USER>\Documents\4Corrosion\Dataset\ss00511_volume_data"
$blobListFile = "$env:TEMP\blob_list.txt"
$volumeListFile = "$env:TEMP\volume_data_list.txt"

# === 1. LISTER TOUS LES BLOBS ===
Write-Host "`n📦 Listing all blobs under $basePath ..."
Start-Process -FilePath ".\azcopy.exe" -ArgumentList "list `"$baseUrl$sasToken`" --output-type=text" -NoNewWindow -Wait -RedirectStandardOutput $blobListFile

# === 2. FILTRER CEUX QUI ONT /volume_data/ DANS LEUR CHEMIN ===
Write-Host "🔍 Filtering for 'volume_data' files only..."
Get-Content $blobListFile | Where-Object { $_ -match "/volume_data/" } | ForEach-Object { $_.Trim() } | Set-Content $volumeListFile

# === 3. TÉLÉCHARGER UNIQUEMENT LES FICHIERS volume_data
Write-Host "`n⬇️ Downloading volume_data folders..."
$env:AZCOPY_CRED_TYPE = "Anonymous"
.\azcopy.exe copy "$baseUrl$sasToken" "$destination" `
  --from-to=BlobLocal `
  --list-of-files="$volumeListFile" `
  --overwrite=true `
  --log-level=INFO
$env:AZCOPY_CRED_TYPE = ""

Write-Host "`n✅ Terminé ! Les dossiers volume_data ont été téléchargés dans : $destination"
