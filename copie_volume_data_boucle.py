import os
import shutil

# 🔧 Dossier racine global à parcourir
ROOT_DATASET_DIR = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\prod_data_dataset"

# 📥 Dossier global de sortie pour tous les fichiers copiés
GLOBAL_OUTPUT_DIR = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\prod_data_dataset\collected_volume_data"
os.makedirs(GLOBAL_OUTPUT_DIR, exist_ok=True)

def find_and_copy_volume_data(root_dir):
    found_any = False

    for dirpath, dirnames, filenames in os.walk(root_dir):
        if os.path.basename(dirpath).lower() == "volume_data":
            print(f"📁 volume_data trouvé : {dirpath}")
            # Construire le préfixe à partir des deux premiers dossiers après prod_data_dataset
            rel_path = os.path.relpath(dirpath, ROOT_DATASET_DIR)
            parts = rel_path.split(os.sep)
            if len(parts) >= 2:
                prefix = f"{parts[0]}_{parts[1]}"
            else:
                prefix = parts[0]

            for filename in filenames:
                src_path = os.path.join(dirpath, filename)
                new_filename = f"{prefix}_{filename}"
                dst_path = os.path.join(GLOBAL_OUTPUT_DIR, new_filename)
                shutil.copy2(src_path, dst_path)
                print(f"✅ Copié : {src_path} → {dst_path}")
                found_any = True

    if not found_any:
        print(f"⚠️ Aucun volume_data trouvé dans : {root_dir}")

def process_all_folders(root_dataset_dir):
    print("🌀 Démarrage du traitement de tous les dossiers...")
    for entry in os.listdir(root_dataset_dir):
        level1_path = os.path.join(root_dataset_dir, entry)
        if os.path.isdir(level1_path):
            for subentry in os.listdir(level1_path):
                level2_path = os.path.join(level1_path, subentry)
                if os.path.isdir(level2_path):
                    print(f"\n🚀 Traitement : {entry}/{subentry}")
                    find_and_copy_volume_data(level2_path)
    print("\n✅ Tous les fichiers ont été collectés dans :", GLOBAL_OUTPUT_DIR)

if __name__ == "__main__":
    process_all_folders(ROOT_DATASET_DIR)
