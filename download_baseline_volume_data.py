#!/usr/bin/env python3
"""
Script pour télécharger les dossiers volume_data des datasets d'un baseline
en utilisant AzCopy, basé sur retrieve_data_asset.py et copy.sh
"""

import argparse
import os
import subprocess
import tempfile
import re
from pathlib import Path
from omegaconf import OmegaConf
from typing import List, Set

# Configuration Azure
BASE_PATH = "Data/Prod_data_dataset"
BASE_URL = f"https://evidentai.blob.core.windows.net/tpi/{BASE_PATH}"
SAS_TOKEN = "?sv=2023-01-03&se=2025-08-24T12%3A50%3A57Z&sr=c&sp=rl&sig=fIDJkquvkUHsL3t6zgIIJe9mj7O2xoUY7tXBz2BR1aQ%3D"

def extract_dataset_ids(dataset_names: List[str]) -> Set[str]:
    """
    Extrait les IDs des datasets (ex: SS00374, PS00389, UW00416, DW000335)
    """
    dataset_ids = set()
    
    for dataset_name in dataset_names:
        # Chercher des patterns comme SS00374, PS00389, UW00416, DW000335, etc.
        matches = re.findall(r'[A-Z]{2}[0-9]+', dataset_name)
        if matches:
            dataset_ids.add(matches[0])
        else:
            print(f"  ⚠️  Aucun ID trouvé pour: {dataset_name}")
    
    return dataset_ids

def run_azcopy_command(cmd: List[str]) -> tuple:
    """
    Exécute une commande azcopy et retourne le code de sortie et la sortie
    """
    try:
        # Exécuter avec sortie en temps réel
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            env={**os.environ, "AZCOPY_CRED_TYPE": "Anonymous"},
            bufsize=1,
            universal_newlines=True
        )

        output_lines = []

        # Lire la sortie ligne par ligne
        for line in process.stdout:
            line = line.strip()
            if line:
                print(f"  {line}")
                output_lines.append(line)

        # Attendre la fin du processus
        process.wait()

        return process.returncode, "\n".join(output_lines), ""

    except KeyboardInterrupt:
        print("\n⚠️  Interruption détectée, arrêt du téléchargement...")
        if 'process' in locals():
            process.terminate()
        return 1, "", "Interrupted by user"
    except Exception as e:
        return 1, "", str(e)

def download_volume_data_for_ids(dataset_ids: Set[str], output_dir: str, azcopy_path: str = "./azcopy.exe") -> bool:
    """
    Télécharge les dossiers volume_data pour les IDs de datasets spécifiés
    """
    if not dataset_ids:
        print("❌ Aucun ID de dataset valide trouvé!")
        return False
    
    print(f"📋 IDs de datasets à télécharger: {sorted(dataset_ids)}")
    print(f"📁 Destination: {output_dir}")
    print("")
    
    # Créer le dossier de destination
    os.makedirs(output_dir, exist_ok=True)
    
    # Créer les patterns d'inclusion pour AzCopy
    include_patterns = []
    for dataset_id in dataset_ids:
        include_patterns.append(f"*/{dataset_id}/*/volume_data/*")
    
    include_pattern_str = ";".join(include_patterns)
    print(f"🔍 Patterns d'inclusion: {include_pattern_str}")
    print("")
    
    # Commande AzCopy
    azcopy_cmd = [
        azcopy_path,
        "copy",
        f"{BASE_URL}{SAS_TOKEN}",
        output_dir,
        "--from-to=BlobLocal",
        f"--include-pattern={include_pattern_str}",
        "--overwrite=true",
        "--log-level=INFO",
        "--recursive=true"
    ]
    
    print("⬇️  Démarrage du téléchargement avec AzCopy...")
    print(f"🔧 Commande: {' '.join(azcopy_cmd[:3])} [URL_WITH_SAS] {output_dir} [OPTIONS]")
    print("")
    
    # Exécuter AzCopy
    exit_code, stdout, stderr = run_azcopy_command(azcopy_cmd)
    
    if stdout:
        print("📄 Sortie AzCopy:")
        print(stdout)
    
    if stderr and exit_code != 0:
        print("❌ Erreurs AzCopy:")
        print(stderr)
    
    if exit_code == 0:
        print("✅ Téléchargement terminé avec succès!")
        return True
    else:
        print(f"❌ Téléchargement échoué avec le code: {exit_code}")
        return False

def main(args):
    """
    Fonction principale
    """
    # Charger le fichier YAML du baseline
    try:
        cfg = OmegaConf.load(args.baseline)
    except Exception as e:
        print(f"❌ Erreur lors du chargement du fichier YAML: {e}")
        return 1
    
    baseline_name = Path(args.baseline).stem
    print(f"🚀 Démarrage du téléchargement pour le baseline: {baseline_name}")
    print(f"📄 Fichier YAML: {args.baseline}")
    
    # Collecter tous les noms de datasets
    all_dataset_names = []
    splits = list(cfg.keys())
    
    if args.section and args.section in splits:
        # Télécharger seulement la section spécifiée
        splits = [args.section]
        print(f"📂 Section sélectionnée: {args.section}")
    else:
        print(f"📂 Sections: {', '.join(splits)}")
    
    for split in splits:
        print(f"\n📋 Traitement de la section '{split}'...")
        split_datasets = []
        
        for asset_info in cfg[split]:
            asset_name = asset_info.split(':')[0]  # Enlever la version
            split_datasets.append(asset_name)
            all_dataset_names.append(asset_name)
        
        print(f"  ✓ {len(split_datasets)} datasets trouvés dans '{split}'")
    
    total_datasets = len(all_dataset_names)
    print(f"\n📊 Total des datasets: {total_datasets}")
    
    if total_datasets == 0:
        print("❌ Aucun dataset trouvé!")
        return 1
    
    # Extraire les IDs uniques des datasets
    print("\n🔍 Extraction des IDs de datasets...")
    dataset_ids = extract_dataset_ids(all_dataset_names)
    
    print(f"📈 IDs uniques extraits: {len(dataset_ids)}")
    
    # Créer le dossier de sortie
    output_dir = os.path.join(args.output_dir, f"{baseline_name}_volume_data")
    
    # Télécharger les données
    print("\n" + "="*60)
    success = download_volume_data_for_ids(dataset_ids, output_dir, args.azcopy_path)
    
    print("\n" + "="*60)
    if success:
        print("🎉 Processus de téléchargement terminé avec succès!")
        print(f"📁 Données sauvegardées dans: {output_dir}")
        return 0
    else:
        print("❌ Échec du processus de téléchargement!")
        return 1

def parse_args():
    """
    Configuration des arguments de ligne de commande
    """
    parser = argparse.ArgumentParser(
        description="Télécharge les dossiers volume_data des datasets d'un baseline"
    )
    
    parser.add_argument(
        "--baseline",
        type=str,
        default="./TPI_calibprod_binary_v9.yml",
        help="Chemin vers le fichier YAML du baseline"
    )
    
    parser.add_argument(
        "--output_dir",
        type=str,
        default="./data",
        help="Dossier de destination pour les données"
    )
    
    parser.add_argument(
        "--section",
        type=str,
        choices=["training", "validation", "testinference"],
        help="Section spécifique à télécharger (optionnel)"
    )
    
    parser.add_argument(
        "--azcopy_path",
        type=str,
        default="./azcopy.exe",
        help="Chemin vers l'exécutable azcopy"
    )
    
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_args()
    exit_code = main(args)
    exit(exit_code)
