import os
import shutil

# 🔧 Dossier à aplatir
TARGET_DIR = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\prod_data_dataset\collected_volume_data\masques_BU"

def flatten_directory(root_dir):
    moved_files = 0
    for dirpath, dirnames, filenames in os.walk(root_dir):
        if dirpath == root_dir:
            continue  # ne rien faire sur le dossier racine lui-même
        for filename in filenames:
            src = os.path.join(dirpath, filename)
            dst = os.path.join(root_dir, filename)

            # Renommer si conflit
            if os.path.exists(dst):
                base, ext = os.path.splitext(filename)
                i = 1
                while os.path.exists(dst):
                    new_name = f"{base}_{i}{ext}"
                    dst = os.path.join(root_dir, new_name)
                    i += 1

            shutil.move(src, dst)
            print(f"✅ Déplacé : {src} → {dst}")
            moved_files += 1

    # Nettoyer les dossiers vides
    for dirpath, dirnames, filenames in os.walk(root_dir, topdown=False):
        if dirpath != root_dir and not dirnames and not filenames:
            os.rmdir(dirpath)
            print(f"🗑️ Supprimé dossier vide : {dirpath}")

    print(f"\n✅ Total de fichiers déplacés : {moved_files}")

if __name__ == "__main__":
    print(f"🚀 Aplatissement de : {TARGET_DIR}")
    flatten_directory(TARGET_DIR)
