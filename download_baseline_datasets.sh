#!/bin/bash

# === CONFIGURATION ===
BASE_PATH="Data/Prod_data_dataset"
BASE_URL="https://evidentai.blob.core.windows.net/tpi/$BASE_PATH"
SAS_TOKEN="?sv=2023-01-03&se=2025-08-24T12%3A50%3A57Z&sr=c&sp=rl&sig=fIDJkquvkUHsL3t6zgIIJe9mj7O2xoUY7tXBz2BR1aQ%3D"
DEST_FOLDER="$HOME/Documents/4Corrosion/Dataset/baseline_datasets"
BLOB_LIST="/tmp/blob_list.txt"
DATASET_LIST="/tmp/dataset_list.txt"

# === FONCTIONS ===
usage() {
    echo "Usage: $0 <yaml_file> [section]"
    echo ""
    echo "Arguments:"
    echo "  yaml_file    Path to the YAML baseline file (e.g., TPI_calibprod_binary_v9.yml)"
    echo "  section      Optional: specific section to download (training, validation, testinference)"
    echo "               If not specified, downloads all sections"
    echo ""
    echo "Examples:"
    echo "  $0 TPI_calibprod_binary_v9.yml"
    echo "  $0 TPI_calibprod_binary_v9.yml training"
    echo "  $0 TPI_calibprod_binary_v9.yml validation"
}

parse_yaml_datasets() {
    local yaml_file="$1"
    local target_section="$2"
    
    if [[ ! -f "$yaml_file" ]]; then
        echo "❌ Error: YAML file '$yaml_file' not found!"
        exit 1
    fi
    
    echo "📋 Parsing datasets from $yaml_file..."
    
    # Clear the dataset list
    > "$DATASET_LIST"
    
    local current_section=""
    local in_target_section=false
    
    while IFS= read -r line; do
        # Remove leading/trailing whitespace
        line=$(echo "$line" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
        
        # Skip empty lines and comments
        [[ -z "$line" || "$line" =~ ^# ]] && continue
        
        # Check if this is a section header (ends with :)
        if [[ "$line" =~ ^([a-zA-Z_]+):$ ]]; then
            current_section="${BASH_REMATCH[1]}"
            if [[ -z "$target_section" || "$current_section" == "$target_section" ]]; then
                in_target_section=true
                echo "📂 Processing section: $current_section"
            else
                in_target_section=false
            fi
            continue
        fi
        
        # If we're in the target section and this is a dataset line
        if [[ "$in_target_section" == true && "$line" =~ ^-[[:space:]]+(.+)$ ]]; then
            dataset_entry="${BASH_REMATCH[1]}"
            # Extract dataset name (before the colon)
            dataset_name=$(echo "$dataset_entry" | cut -d':' -f1)
            echo "  ✓ Found dataset: $dataset_name"
            echo "$dataset_name" >> "$DATASET_LIST"
        fi
    done < "$yaml_file"
    
    local dataset_count=$(wc -l < "$DATASET_LIST")
    echo "📊 Total datasets to download: $dataset_count"
    
    if [[ $dataset_count -eq 0 ]]; then
        echo "⚠️  No datasets found to download!"
        exit 1
    fi
}

download_datasets() {
    echo ""
    echo "📦 Listing all blobs under $BASE_PATH..."
    "$AZCOPY_CMD" list "${BASE_URL}${SAS_TOKEN}" --output-type=text > "$BLOB_LIST"
    
    if [[ ! -s "$BLOB_LIST" ]]; then
        echo "❌ Error: Failed to list blobs or no blobs found!"
        exit 1
    fi
    
    echo "🔍 Filtering blobs for target datasets..."
    
    # Create a temporary file for matching blobs
    local matching_blobs="/tmp/matching_blobs.txt"
    > "$matching_blobs"
    
    # For each dataset, find matching blobs
    while IFS= read -r dataset_name; do
        echo "  🔎 Searching for: $dataset_name"

        # Extract potential identifiers from dataset name
        # Look for patterns like SS00374, PS00389, UW00416, DW000335, etc.
        dataset_id=$(echo "$dataset_name" | grep -o '[A-Z][A-Z][0-9]\+' | head -1)

        if [[ -n "$dataset_id" ]]; then
            echo "    📍 Extracted ID: $dataset_id"

            # Search for blobs containing the dataset ID
            grep -i "/$dataset_id/" "$BLOB_LIST" | awk -F';' '{print $1}' >> "$matching_blobs"

            # Also search for volume_data specifically for this dataset ID
            grep -i "/$dataset_id.*volume_data" "$BLOB_LIST" | awk -F';' '{print $1}' >> "$matching_blobs"
        else
            echo "    ⚠️  No ID pattern found, trying full name"
            # Fallback: search for blobs containing the dataset name
            grep -i "/$dataset_name/" "$BLOB_LIST" | awk -F';' '{print $1}' >> "$matching_blobs"

            # Also search for volume_data specifically for this dataset
            grep -i "/$dataset_name.*volume_data" "$BLOB_LIST" | awk -F';' '{print $1}' >> "$matching_blobs"
        fi

    done < "$DATASET_LIST"
    
    # Remove duplicates and empty lines
    sort "$matching_blobs" | uniq | grep -v '^$' > "${matching_blobs}.clean"
    mv "${matching_blobs}.clean" "$matching_blobs"
    
    local blob_count=$(wc -l < "$matching_blobs")
    echo ""
    echo "📋 Found $blob_count matching blobs:"
    head -10 "$matching_blobs"
    if [[ $blob_count -gt 10 ]]; then
        echo "  ... and $((blob_count - 10)) more"
    fi
    
    if [[ $blob_count -eq 0 ]]; then
        echo "⚠️  No matching blobs found for the specified datasets!"
        exit 1
    fi
    
    echo ""
    echo "⬇️  Starting download to: $DEST_FOLDER"
    echo "🕐 This may take a while depending on the data size..."
    
    # Create destination folder if it doesn't exist
    mkdir -p "$DEST_FOLDER"
    
    # Download using azcopy with the list of matching files
    AZCOPY_CRED_TYPE=Anonymous "$AZCOPY_CMD" copy "${BASE_URL}${SAS_TOKEN}" "$DEST_FOLDER" \
      --from-to=BlobLocal \
      --list-of-files="$matching_blobs" \
      --overwrite=true \
      --log-level=INFO \
      --recursive=true
    
    local exit_code=$?
    
    # Cleanup temporary files
    rm -f "$BLOB_LIST" "$DATASET_LIST" "$matching_blobs"
    
    if [[ $exit_code -eq 0 ]]; then
        echo ""
        echo "✅ Download completed successfully!"
        echo "📁 Data saved to: $DEST_FOLDER"
    else
        echo ""
        echo "❌ Download failed with exit code: $exit_code"
        exit $exit_code
    fi
}

# === MAIN SCRIPT ===
if [[ $# -lt 1 || $# -gt 2 ]]; then
    usage
    exit 1
fi

YAML_FILE="$1"
SECTION="$2"

echo "🚀 Starting baseline dataset download..."
echo "📄 YAML file: $YAML_FILE"
if [[ -n "$SECTION" ]]; then
    echo "📂 Section: $SECTION"
else
    echo "📂 Section: ALL"
fi
echo ""

# Check if azcopy exists (try both azcopy and azcopy.exe)
AZCOPY_CMD=""
if [[ -f "./azcopy" ]]; then
    AZCOPY_CMD="./azcopy"
elif [[ -f "./azcopy.exe" ]]; then
    AZCOPY_CMD="./azcopy.exe"
else
    echo "❌ Error: azcopy not found in current directory!"
    echo "Please make sure azcopy or azcopy.exe is available in the current directory."
    exit 1
fi

echo "🔧 Using azcopy: $AZCOPY_CMD"

# Parse YAML and extract datasets
parse_yaml_datasets "$YAML_FILE" "$SECTION"

# Download the datasets
download_datasets

echo ""
echo "🎉 All done!"
